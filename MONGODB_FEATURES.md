# MongoDB 读取功能

本项目实现了四个主要的MongoDB读取功能，用于从copilot数据库中获取用户相关数据。

## 功能概述

### 1. 根据用户ID获取查询历史
- **集合**: `copilot.gem_task_response`
- **功能**: 根据`user_id`筛选文档，提取`query`字段
- **过滤**: 自动过滤无效查询（如"Style with this"）
- **返回**: 有效查询字符串数组

### 2. 根据用户ID获取保存的产品文档ID
- **集合**: `copilot.user_saved_collections`
- **功能**: 根据`user_id`和`collection_type='product'`筛选文档，提取`document_id`字段
- **返回**: 文档ID字符串数组

### 3. 根据ID列表获取产品详细信息
- **集合**: `copilot.deal_info`
- **功能**: 解析ID格式`"{product_id}-{host}"`，匹配`product_id`和`host`字段
- **返回**: 产品信息对象数组，包含`host`、`product_id`、`brand`、`title`、`categories`字段

### 4. 根据用户ID获取用户偏好设置
- **集合**: `copilot.user_preference`
- **功能**: 根据`user_id`筛选文档，提取`preference`字段
- **返回**: 用户偏好对象（Record<string, any>）或null（如果未找到）
- **缓存**: 支持智能缓存，仅当偏好不为null时才缓存

## 文件结构

```
src/
├── utils/
│   ├── mongodb.ts              # 核心MongoDB操作函数
│   ├── mongodbClient.ts        # 客户端工具类
│   └── __tests__/
│       └── mongodb.test.ts     # 测试文件
├── app/api/mongodb/
│   └── route.ts               # API路由
└── examples/
    └── mongodbUsage.ts        # 使用示例
```

## 环境配置

在`.env.local`文件中设置MongoDB连接URL：

```env
mongoUrl=********************************:port/database
```

## 使用方法

### 方法1: 使用客户端工具类（推荐）

```typescript
import { 
  getQueriesByUserId, 
  getDocumentIdsByUserId, 
  getProductInfoByIds 
} from '@/utils/mongodbClient'

// 获取用户查询历史
const queries = await getQueriesByUserId('user123')

// 获取用户保存的文档ID
const documentIds = await getDocumentIdsByUserId('user123')

// 获取产品详细信息
const productInfos = await getProductInfoByIds(['12345-amazon.com', '67890-ebay.com'])
```

### 方法2: 直接调用API

```typescript
// 获取查询历史
const response = await fetch('/api/mongodb', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'getQueries',
    userId: 'user123'
  })
})

const result = await response.json()
if (result.success) {
  console.log('查询历史:', result.data)
}
```

### 方法3: 服务端直接使用

```typescript
import { 
  getQueriesByUserId, 
  getDocumentIdsByUserId, 
  getProductInfoByIds 
} from '@/utils/mongodb'

// 在API路由或服务端组件中使用
export async function GET() {
  const queries = await getQueriesByUserId('user123')
  return NextResponse.json({ queries })
}
```

## API 接口

### POST /api/mongodb

#### 获取查询历史
```json
{
  "action": "getQueries",
  "userId": "user123"
}
```

#### 获取文档ID
```json
{
  "action": "getDocumentIds", 
  "userId": "user123"
}
```

#### 获取产品信息
```json
{
  "action": "getProductInfo",
  "ids": ["12345-amazon.com", "67890-ebay.com"]
}
```

#### 响应格式
```json
{
  "success": true,
  "data": [...] // 具体数据
}
```

或错误响应：
```json
{
  "success": false,
  "error": "错误信息"
}
```

## 数据类型

### ProductInfo 接口
```typescript
interface ProductInfo {
  host: string          // 主机名
  product_id: string    // 产品ID
  brand: string         // 品牌
  title: string         // 标题
  categories: string[]  // 分类数组
}
```

## 错误处理

所有函数都包含完整的错误处理：

- **连接错误**: MongoDB连接失败
- **配置错误**: 缺少mongoUrl环境变量
- **数据验证错误**: 无效的输入参数
- **查询错误**: 数据库查询失败

## 性能考虑

- **连接复用**: 自动管理MongoDB连接，避免重复连接
- **只读操作**: 所有操作都是只读的，不会修改数据
- **无聚合**: 按要求不使用MongoDB聚合功能
- **批量查询**: `getProductInfoByIds`支持批量获取产品信息

## 测试

运行测试：
```bash
npm test src/utils/__tests__/mongodb.test.ts
```

注意：测试需要真实的MongoDB连接和测试数据。

## 示例用法

查看 `src/examples/mongodbUsage.ts` 文件获取完整的使用示例，包括：

- 基本功能使用
- 完整工作流程
- 错误处理
- 自定义客户端

## 注意事项

1. **环境变量**: 确保正确设置`mongoUrl`环境变量
2. **数据库权限**: 确保MongoDB用户有读取权限
3. **网络连接**: 确保应用能够访问MongoDB服务器
4. **数据格式**: ID格式必须为`"{product_id}-{host}"`
5. **错误处理**: 始终使用try-catch包装异步调用

## 扩展功能

如需添加新的MongoDB读取功能：

1. 在`src/utils/mongodb.ts`中添加新函数
2. 在`src/app/api/mongodb/route.ts`中添加新的action处理
3. 在`src/utils/mongodbClient.ts`中添加客户端方法
4. 添加相应的测试用例
