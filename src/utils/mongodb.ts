import { Collection, Db, MongoClient } from "mongodb"

import {
  COLLECTIONS,
  GemTaskResponseDocument,
  isValidQuery,
  ProductInfo,
  UserPreferenceDocument,
  UserSavedCollectionsDocument,
} from "@/types/mongodb"

// MongoDB连接实例
let client: MongoClient | null = null
let db: Db | null = null

// 缓存配置
interface CacheConfig {
  enabled: boolean
  ttl: number // 缓存时间（毫秒）
}

interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
}

// 默认缓存配置
const DEFAULT_CACHE_CONFIG: CacheConfig = {
  enabled: true,
  ttl: 5 * 60 * 1000, // 5分钟
}

// 内存缓存存储
const cache = {
  queries: new Map<string, CacheEntry<string[]>>(),
  documentIds: new Map<string, CacheEntry<string[]>>(),
  products: new Map<string, CacheEntry<ProductInfo>>(),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  userPreferences: new Map<string, CacheEntry<Record<string, any>>>(),
}

// 缓存工具函数
function isExpired<T>(entry: CacheEntry<T>): boolean {
  return Date.now() - entry.timestamp > entry.ttl
}

function setCache<T>(
  cacheMap: Map<string, CacheEntry<T>>,
  key: string,
  data: T,
  ttl: number = DEFAULT_CACHE_CONFIG.ttl,
): void {
  cacheMap.set(key, {
    data,
    timestamp: Date.now(),
    ttl,
  })
}

function getCache<T>(
  cacheMap: Map<string, CacheEntry<T>>,
  key: string,
): T | null {
  const entry = cacheMap.get(key)
  if (!entry || isExpired(entry)) {
    if (entry) {
      cacheMap.delete(key)
    }
    return null
  }
  return entry.data
}

// 清理过期缓存的定时器
setInterval(() => {
  const now = Date.now()

  // 清理查询缓存
  for (const [key, entry] of cache.queries.entries()) {
    if (now - entry.timestamp > entry.ttl) {
      cache.queries.delete(key)
    }
  }

  // 清理文档ID缓存
  for (const [key, entry] of cache.documentIds.entries()) {
    if (now - entry.timestamp > entry.ttl) {
      cache.documentIds.delete(key)
    }
  }

  // 清理产品缓存
  for (const [key, entry] of cache.products.entries()) {
    if (now - entry.timestamp > entry.ttl) {
      cache.products.delete(key)
    }
  }

  // 清理用户偏好缓存
  for (const [key, entry] of cache.userPreferences.entries()) {
    if (now - entry.timestamp > entry.ttl) {
      cache.userPreferences.delete(key)
    }
  }
}, 60 * 1000) // 每分钟清理一次

/**
 * 获取MongoDB连接
 */
async function getDatabase(): Promise<Db> {
  if (!db) {
    const mongoUrl = process.env.MONGODB_CONN
    if (!mongoUrl) {
      throw new Error("MongoDB URL not found in environment variables")
    }

    client = new MongoClient(mongoUrl)
    await client.connect()
    db = client.db("copilot")
  }
  return db
}

/**
 * 关闭MongoDB连接
 */
export async function closeConnection(): Promise<void> {
  if (client) {
    await client.close()
    client = null
    db = null
  }
}

/**
 * 根据user_id读取gem_task_response集合，提取query字段
 * @param userId 用户ID
 * @param useCache 是否使用缓存，默认为true
 * @returns query列表，过滤掉无效query
 */
export async function getQueriesByUserId(
  userId: string,
  useCache: boolean = DEFAULT_CACHE_CONFIG.enabled,
): Promise<string[]> {
  try {
    // 检查缓存
    if (useCache) {
      const cacheKey = `queries:${userId}`
      const cachedData = getCache(cache.queries, cacheKey)
      if (cachedData !== null) {
        return cachedData
      }
    }

    const database = await getDatabase()
    const collection: Collection<GemTaskResponseDocument> = database.collection(
      COLLECTIONS.GEM_TASK_RESPONSE,
    )

    // 查询匹配user_id的文档
    const documents = await collection.find({ user_id: userId }).toArray()

    // 提取query字段并过滤无效值
    const queries: string[] = []

    for (const doc of documents) {
      if (
        doc.query &&
        typeof doc.query === "string" &&
        isValidQuery(doc.query)
      ) {
        queries.push(doc.query)
      }
    }

    // 缓存结果
    if (useCache) {
      const cacheKey = `queries:${userId}`
      setCache(cache.queries, cacheKey, queries)
    }

    return queries
  } catch (error) {
    console.error("Error fetching queries by user_id:", error)
    throw error
  }
}

/**
 * 根据user_id读取user_saved_collections集合，提取document_id字段
 * @param userId 用户ID
 * @param useCache 是否使用缓存，默认为true
 * @returns document_id列表
 */
export async function getDocumentIdsByUserId(
  userId: string,
  useCache: boolean = DEFAULT_CACHE_CONFIG.enabled,
): Promise<string[]> {
  try {
    // 检查缓存
    if (useCache) {
      const cacheKey = `documentIds:${userId}`
      const cachedData = getCache(cache.documentIds, cacheKey)
      if (cachedData !== null) {
        return cachedData
      }
    }

    const database = await getDatabase()
    const collection: Collection<UserSavedCollectionsDocument> =
      database.collection(COLLECTIONS.USER_SAVED_COLLECTIONS)

    // 查询匹配user_id且collection_type为product的文档
    const documents = await collection
      .find({
        user_id: userId,
        collection_type: "product",
      })
      .toArray()

    // 提取document_id字段
    const documentIds: string[] = []

    for (const doc of documents) {
      if (doc.document_id && typeof doc.document_id === "string") {
        documentIds.push(doc.document_id)
      }
    }

    // 缓存结果
    if (useCache) {
      const cacheKey = `documentIds:${userId}`
      setCache(cache.documentIds, cacheKey, documentIds)
    }

    return documentIds
  } catch (error) {
    console.error("Error fetching document_ids by user_id:", error)
    throw error
  }
}

/**
 * 根据ID列表获取产品信息
 * @param ids ID列表，格式为"{product_id}-{host}"
 * @param useCache 是否使用缓存，默认为true
 * @returns 产品信息列表
 */
export async function getProductInfoByIds(
  ids: string[],
  useCache: boolean = DEFAULT_CACHE_CONFIG.enabled,
): Promise<ProductInfo[]> {
  try {
    const productInfos: ProductInfo[] = []
    const uncachedIds: string[] = []

    // 检查缓存，分离已缓存和未缓存的ID
    if (useCache) {
      for (const id of ids) {
        const cacheKey = `product:${id}`
        const cachedData = getCache(cache.products, cacheKey)
        if (cachedData !== null) {
          productInfos.push(cachedData)
        } else {
          uncachedIds.push(id)
        }
      }
    } else {
      uncachedIds.push(...ids)
    }

    // 如果所有数据都已缓存，直接返回
    if (uncachedIds.length === 0) {
      return productInfos
    }

    // 获取未缓存的产品信息
    const results = await Promise.allSettled(
      uncachedIds.map(async (id) => {
        const i = id.lastIndexOf("-")
        if (i === -1) throw new Error(`Invalid ID: ${id}`)

        const res = await fetch(
          `https://gem-workflow.favie.yesy.online/product/${id}`,
          {
            cache: "no-store",
          },
        )
        if (!res.ok) throw new Error(`HTTP ${res.status} for ${id}`)

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const doc: any = await res.json()
        if (!doc) return null

        const info: ProductInfo = {
          product_id: doc.global_id || "",
          host: doc.platform || "",
          brand: doc.brand || "",
          title: doc.title || "",
          categories: [doc.tags?.cate_tag].filter(Boolean),
        }

        // 缓存单个产品信息
        if (useCache) {
          const cacheKey = `product:${id}`
          setCache(cache.products, cacheKey, info)
        }

        return info
      }),
    )

    const newProductInfos = results
      .filter(
        (r): r is PromiseFulfilledResult<ProductInfo | null> =>
          r.status === "fulfilled",
      )
      .map((r) => r.value)
      .filter((v): v is ProductInfo => v !== null)

    // 合并缓存的和新获取的产品信息
    productInfos.push(...newProductInfos)

    return productInfos
  } catch (error) {
    console.error("Error fetching product info by ids:", error)
    throw error
  }
}

/**
 * 根据user_id读取user_preference集合，提取preference字段
 * @param userId 用户ID
 * @param useCache 是否使用缓存，默认为true
 * @returns 用户偏好对象，如果未找到则返回null
 */
export async function getUserPreferenceByUserId(
  userId: string,
  useCache: boolean = DEFAULT_CACHE_CONFIG.enabled,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
): Promise<Record<string, any> | null> {
  try {
    // 检查缓存
    if (useCache) {
      const cacheKey = `userPreference:${userId}`
      const cachedData = getCache(cache.userPreferences, cacheKey)
      if (cachedData !== null) {
        return cachedData
      }
    }

    const database = await getDatabase()
    const collection: Collection<UserPreferenceDocument> = database.collection(
      COLLECTIONS.USER_PREFERENCE,
    )

    // 查询匹配user_id的文档
    const document = await collection.findOne({ user_id: userId })

    // 提取preference字段
    const preference = document?.preference || null

    // 缓存结果
    if (useCache && preference !== null) {
      const cacheKey = `userPreference:${userId}`
      setCache(cache.userPreferences, cacheKey, preference)
    }

    return preference
  } catch (error) {
    console.error("Error fetching user preference by user_id:", error)
    throw error
  }
}

/**
 * 清空所有缓存
 */
export function clearAllCache(): void {
  cache.queries.clear()
  cache.documentIds.clear()
  cache.products.clear()
  cache.userPreferences.clear()
}

/**
 * 清空指定用户的查询缓存
 * @param userId 用户ID
 */
export function clearQueriesCache(userId: string): void {
  const cacheKey = `queries:${userId}`
  cache.queries.delete(cacheKey)
}

/**
 * 清空指定用户的文档ID缓存
 * @param userId 用户ID
 */
export function clearDocumentIdsCache(userId: string): void {
  const cacheKey = `documentIds:${userId}`
  cache.documentIds.delete(cacheKey)
}

/**
 * 清空指定产品的缓存
 * @param productId 产品ID，格式为"{product_id}-{host}"
 */
export function clearProductCache(productId: string): void {
  const cacheKey = `product:${productId}`
  cache.products.delete(cacheKey)
}

/**
 * 清空指定用户的偏好缓存
 * @param userId 用户ID
 */
export function clearUserPreferenceCache(userId: string): void {
  const cacheKey = `userPreference:${userId}`
  cache.userPreferences.delete(cacheKey)
}

/**
 * 获取缓存统计信息
 */
export function getCacheStats(): {
  queries: number
  documentIds: number
  products: number
  userPreferences: number
  total: number
} {
  return {
    queries: cache.queries.size,
    documentIds: cache.documentIds.size,
    products: cache.products.size,
    userPreferences: cache.userPreferences.size,
    total:
      cache.queries.size +
      cache.documentIds.size +
      cache.products.size +
      cache.userPreferences.size,
  }
}
