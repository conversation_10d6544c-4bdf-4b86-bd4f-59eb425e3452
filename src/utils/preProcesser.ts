"use server"

import {
  getDocumentIdsByUserId,
  getProductInfoByIds,
  getQueriesByUserId,
  getUserPreferenceByUserId,
} from "./mongodb"

export async function inputProcesser(message: string) {
  const preference = await getUserPreferenceByUserId(message)
  console.log("---- step: preference", preference)

  const queryList = await getQueriesByUserId(message)
  console.log("---- step: query list", queryList.length)

  const pidList = await getDocumentIdsByUserId(message)
  console.log("---- step: product ids", pidList.length)

  const pList = await getProductInfoByIds(pidList)
  console.log("---- step: product list", pList.length)

  const q = {
    preference: {
      gender: preference?.gender,
      body_type: preference?.body_type?.[0],
      age_group: preference?.age_group,
      race: preference?.race,
    },
    queries: queryList,
    products: pList.map((it) => ({
      brand: it.brand,
      description: it.title,
      categories: it.categories?.[0],
      platform: it.host,
    })),
  }
  console.log("---- q", q)

  return JSON.stringify(q)
}

export async function outputProcesser(response: string) {
  console.log("---- response", response)

  // Try to find JSON in the response
  const jsonMatch = response.match(/\{[\s\S]*\}/)

  if (jsonMatch) {
    const jsonStr = jsonMatch[0]
    const parsed = JSON.parse(jsonStr)

    // Log the parsed JSON for debugging
    console.log("Detected JSON in response:", jsonStr)

    // If there's an output field, return it; otherwise return original response
    if (parsed.output) {
      return `
关键词：
      <主要风格>

        ${parsed.debug.keyword.style.join(", ")}

      <主要品牌>

        ${parsed.debug.keyword.brand.join(", ")}

      <主要款式>

        ${parsed.debug.keyword.type.join(", ")}

描述列表：
      <一句话概括(主观视角)>

        ${parsed.output.bioSubjective}

      <一句话概括(客观视角)>

        ${parsed.output.bioObjective}

      <社媒风格描述>

        ${parsed.output.descriptionSocial}

      <闺蜜风格描述>

        ${parsed.output.descriptionFriend}

      <时尚杂志风格描述>

        ${parsed.output.descriptionMagazine}

      <中性风格描述>

        ${parsed.output.descriptionNeutral}
      `
    }
  }

  // If no JSON found or no output field, return original response
  return response
}
