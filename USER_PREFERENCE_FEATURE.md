# 用户偏好功能实现总结

## 功能概述

成功为项目添加了根据 `user_id` 读取 `copilot.user_preference` 集合的功能，支持提取 `preference` 字段（Object 类型）并提供智能缓存机制。

## 实现的功能

### 1. 核心函数
- **函数名**: `getUserPreferenceByUserId`
- **参数**: 
  - `userId: string` - 用户ID
  - `useCache: boolean = true` - 是否使用缓存（可选）
- **返回值**: `Promise<Record<string, any> | null>`
- **功能**: 从 `copilot.user_preference` 集合中根据 `user_id` 筛选数据，提取 `preference` 字段

### 2. 缓存支持
- **缓存键格式**: `userPreference:{userId}`
- **缓存策略**: 仅当 preference 不为 null 时才缓存
- **TTL**: 5分钟
- **自动清理**: 每分钟清理过期缓存

### 3. API 支持
- **新增 action**: `getUserPreference`
- **请求参数**: `{ action: "getUserPreference", userId: string, useCache?: boolean }`
- **响应格式**: `{ success: boolean, data?: Record<string, any> | null, error?: string }`

## 文件修改清单

### 1. 类型定义 (`src/types/mongodb.ts`)
- ✅ 添加 `UserPreferenceDocument` 接口
- ✅ 在 `COLLECTIONS` 中添加 `USER_PREFERENCE: "user_preference"`
- ✅ 更新 `MongoDBRequest` 接口，添加 `getUserPreference` action
- ✅ 更新 `MongoDBResponse` 泛型类型
- ✅ 添加 `UserPreferenceResponse` 类型

### 2. 核心功能 (`src/utils/mongodb.ts`)
- ✅ 导入 `UserPreferenceDocument` 类型
- ✅ 在缓存存储中添加 `userPreferences` Map
- ✅ 更新缓存清理定时器，包含用户偏好缓存清理
- ✅ 实现 `getUserPreferenceByUserId` 函数
- ✅ 更新 `clearAllCache` 函数
- ✅ 添加 `clearUserPreferenceCache` 函数
- ✅ 更新 `getCacheStats` 函数

### 3. API 路由 (`src/app/api/mongodb/route.ts`)
- ✅ 导入 `getUserPreferenceByUserId` 函数
- ✅ 更新结果类型定义
- ✅ 添加 `getUserPreference` case 处理

### 4. 预处理器 (`src/utils/preProcesser.ts`)
- ✅ 导入 `getUserPreferenceByUserId` 函数
- ✅ 修复错误的函数调用（原来错误地调用了 `getDocumentIdsByUserId`）

### 5. 文档更新
- ✅ 更新 `README.md` - 添加用户偏好功能说明
- ✅ 更新 `CACHE_USAGE.md` - 添加用户偏好缓存说明
- ✅ 更新 `MONGODB_FEATURES.md` - 添加第4个功能说明

### 6. 测试和示例
- ✅ 创建 `src/utils/test-user-preference.ts` - 测试函数
- ✅ 创建 `examples/user-preference-usage.md` - 使用示例

## 使用方法

### 直接函数调用
```typescript
import { getUserPreferenceByUserId } from '@/utils/mongodb'

// 使用缓存
const preference = await getUserPreferenceByUserId('user123')

// 跳过缓存
const freshPreference = await getUserPreferenceByUserId('user123', false)
```

### API 调用
```typescript
const response = await fetch('/api/mongodb', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    action: 'getUserPreference',
    userId: 'user123',
    useCache: true
  })
})

const result = await response.json()
```

### 缓存管理
```typescript
import { 
  clearUserPreferenceCache, 
  getCacheStats 
} from '@/utils/mongodb'

// 清空特定用户缓存
clearUserPreferenceCache('user123')

// 查看缓存统计
const stats = getCacheStats()
console.log(stats.userPreferences) // 用户偏好缓存数量
```

## 技术特点

1. **类型安全**: 完整的 TypeScript 类型定义
2. **智能缓存**: 仅缓存有效数据，避免缓存 null 值
3. **错误处理**: 完善的错误处理和日志记录
4. **一致性**: 与现有功能保持一致的 API 设计
5. **可扩展**: 易于扩展和维护的代码结构

## 测试建议

1. 使用 `src/utils/test-user-preference.ts` 中的测试函数
2. 验证缓存机制是否正常工作
3. 测试 API 端点的正确性
4. 验证错误处理逻辑

## 注意事项

1. 确保 MongoDB 连接配置正确
2. 确保 `copilot.user_preference` 集合存在
3. 注意 `preference` 字段为 null 时不会被缓存
4. 缓存有效期为 5 分钟，可根据需要调整
