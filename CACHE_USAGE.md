# MongoDB 缓存功能使用指南

## 概述

为 `getQueriesByUserId`、`getDocumentIdsByUserId`、`getProductInfoByIds` 和 `getUserPreferenceByUserId` 四个函数添加了内存缓存功能，以提高性能并减少数据库和网络请求。

## 功能特性

### 1. 自动缓存
- **默认启用**: 所有函数默认开启缓存功能
- **TTL**: 缓存有效期为 5 分钟
- **自动清理**: 每分钟自动清理过期缓存

### 2. 缓存粒度
- `getQueriesByUserId`: 按用户ID缓存
- `getDocumentIdsByUserId`: 按用户ID缓存
- `getProductInfoByIds`: 按单个产品ID缓存（精确到每个产品）
- `getUserPreferenceByUserId`: 按用户ID缓存

### 3. 缓存管理
- 支持禁用缓存
- 支持清空指定缓存
- 提供缓存统计信息

## 使用方法

### 基本使用（默认开启缓存）

```typescript
import {
  getQueriesByUserId,
  getDocumentIdsByUserId,
  getProductInfoByIds,
  getUserPreferenceByUserId,
} from '@/utils/mongodb'

// 获取用户查询历史（使用缓存）
const queries = await getQueriesByUserId('user123')

// 获取用户文档ID（使用缓存）
const documentIds = await getDocumentIdsByUserId('user123')

// 获取产品信息（使用缓存）
const products = await getProductInfoByIds(['product1-host1', 'product2-host2'])

// 获取用户偏好（使用缓存）
const preference = await getUserPreferenceByUserId('user123')
```

### 禁用缓存

```typescript
// 强制从数据库获取最新数据
const queries = await getQueriesByUserId('user123', false)
const documentIds = await getDocumentIdsByUserId('user123', false)
const products = await getProductInfoByIds(['product1-host1'], false)
const preference = await getUserPreferenceByUserId('user123', false)
```

### 缓存管理

```typescript
import {
  clearAllCache,
  clearQueriesCache,
  clearDocumentIdsCache,
  clearProductCache,
  clearUserPreferenceCache,
  getCacheStats,
} from '@/utils/mongodb'

// 清空所有缓存
clearAllCache()

// 清空指定用户的查询缓存
clearQueriesCache('user123')

// 清空指定用户的文档ID缓存
clearDocumentIdsCache('user123')

// 清空指定产品的缓存
clearProductCache('product1-host1')

// 清空指定用户的偏好缓存
clearUserPreferenceCache('user123')

// 获取缓存统计信息
const stats = getCacheStats()
console.log('缓存统计:', stats)
// 输出: { queries: 5, documentIds: 3, products: 10, userPreferences: 2, total: 20 }
```

## 缓存策略详解

### 1. getQueriesByUserId 缓存
- **缓存键**: `queries:{userId}`
- **缓存内容**: 过滤后的有效查询字符串数组
- **适用场景**: 用户查询历史不频繁变化

### 2. getDocumentIdsByUserId 缓存
- **缓存键**: `documentIds:{userId}`
- **缓存内容**: 用户保存的产品文档ID数组
- **适用场景**: 用户收藏的产品列表相对稳定

### 3. getProductInfoByIds 缓存（精确到产品级别）
- **缓存键**: `product:{productId}`
- **缓存内容**: 单个产品的详细信息
- **智能合并**: 自动合并缓存命中和未命中的结果
- **适用场景**: 产品信息相对稳定，支持部分缓存命中

### 4. getUserPreferenceByUserId 缓存
- **缓存键**: `userPreference:{userId}`
- **缓存内容**: 用户偏好设置对象（Record<string, any>）
- **缓存条件**: 仅当偏好不为null时才缓存
- **适用场景**: 用户偏好设置相对稳定，减少数据库查询

#### 产品缓存示例
```typescript
// 第一次请求 - 全部从网络获取
const batch1 = await getProductInfoByIds(['p1-h1', 'p2-h2', 'p3-h3'])
// 网络请求: 3次

// 第二次请求 - 部分缓存命中
const batch2 = await getProductInfoByIds(['p1-h1', 'p4-h4'])  
// 网络请求: 1次 (p1-h1从缓存获取，只请求p4-h4)

// 第三次请求 - 全部缓存命中
const batch3 = await getProductInfoByIds(['p1-h1', 'p2-h2'])
// 网络请求: 0次
```

## 性能优势

### 1. 减少数据库查询
- 相同用户的重复查询直接从内存返回
- 避免重复的MongoDB连接和查询操作

### 2. 减少网络请求
- 产品信息按单个产品缓存，最大化缓存命中率
- 支持部分缓存命中，只请求未缓存的产品

### 3. 提升响应速度
- 内存访问速度远快于数据库查询和网络请求
- 缓存命中时响应时间从毫秒级降至微秒级

## 注意事项

### 1. 内存使用
- 缓存存储在应用程序内存中
- 大量数据可能增加内存使用
- 定期清理过期缓存以控制内存占用

### 2. 数据一致性
- 缓存可能导致数据不是最新的
- 对于需要实时数据的场景，建议禁用缓存
- 可以通过清空缓存强制获取最新数据

### 3. 缓存失效
- 缓存有效期为5分钟
- 应用重启会清空所有缓存
- 可以手动清空特定缓存

## 配置选项

当前缓存配置在 `mongodb.ts` 中定义：

```typescript
const DEFAULT_CACHE_CONFIG: CacheConfig = {
  enabled: true,        // 默认启用缓存
  ttl: 5 * 60 * 1000,  // 5分钟TTL
}
```

如需修改配置，可以直接编辑该配置对象。

## 监控和调试

使用 `getCacheStats()` 函数监控缓存使用情况：

```typescript
const stats = getCacheStats()
console.log(`查询缓存: ${stats.queries}`)
console.log(`文档ID缓存: ${stats.documentIds}`)
console.log(`产品缓存: ${stats.products}`)
console.log(`总缓存数: ${stats.total}`)
```

这有助于：
- 了解缓存命中情况
- 监控内存使用
- 调试缓存相关问题
